name: Build and Deploy to EC2

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
      - develop

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18.20.x
          cache: "npm"

      - name: Install dependencies
        run: npm install

      - name: Build the project
        run: |
          echo "${{ secrets.DEV_WEB_ENV_FILE }}" > .env
          npm run build

      - name: Archive production build folder
        uses: actions/upload-artifact@v4
        with:
          name: production-build
          path: build

  checking-secrets:
    needs: build
    if: github.ref == 'refs/heads/master'
    name: Checking secrets
    runs-on: ubuntu-latest
    outputs:
      secret_key_exists: ${{ steps.check_secrets.outputs.defined }}
    steps:
      - name: Check for Secrets availability
        id: check_secrets
        run: |
          if [[
               -n "${{ secrets.EC2_DEV_HOST }}" &&
               -n "${{ secrets.EC2_DEV_USERNAME }}" &&
               -n "${{ secrets.EC2_DEV_SSH_KEY }}" &&
               -n "${{ secrets.DEV_WEB_ENV_FILE }}"
          ]]; then
            echo "defined=true" >> $GITHUB_OUTPUT;
          else
            echo "defined=false" >> $GITHUB_OUTPUT;
          fi

  deploy:
    needs: [build, checking-secrets]
    if: needs.checking-secrets.outputs.secret_key_exists == 'true' && github.ref == 'refs/heads/master'
    runs-on: ubuntu-latest

    steps:
      - name: Download build artifact
        uses: actions/download-artifact@v4
        with:
          name: production-build
          path: build

      - name: Connect to EC2 using SSH and upload files
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.EC2_DEV_HOST }}
          username: ${{ secrets.EC2_DEV_USERNAME }}
          key: ${{ secrets.EC2_DEV_SSH_KEY }}
          port: 22
          source: "build"
          target: "/usr/share/nginx/mccarthy"

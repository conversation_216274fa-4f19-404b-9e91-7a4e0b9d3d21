{"name": "environment-trash-webui", "version": "0.1.0", "private": true, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@fontsource/noto-sans-jp": "^5.0.19", "@iconify/react": "^5.0.1", "@mui/icons-material": "^5.15.20", "@mui/lab": "^5.0.0-alpha.119", "@mui/material": "^5.16.4", "@mui/x-date-pickers": "^5.0.17", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.99", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vis.gl/react-google-maps": "^1.1.0", "axios": "^1.7.2", "bootstrap": "^5.3.3", "date-fns": "^2.30.0", "dayjs": "^1.11.11", "framer-motion": "^11.2.12", "lodash": "^4.17.21", "luxon": "^3.4.4", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-geocode": "^1.0.0-alpha.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.52.0", "react-redux": "^9.1.2", "react-router-dom": "^6.24.1", "react-scripts": "5.0.1", "react-select": "^5.7.0", "react-toastify": "^10.0.5", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "yup": "^1.4.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@types/lodash": "^4.17.6", "@types/react-helmet": "^6.1.11", "sass": "^1.77.6"}}
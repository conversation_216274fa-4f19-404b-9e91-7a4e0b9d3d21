@import '../../assets/styles/variable';

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.spinner {
  border: 4px solid $primary-button-color;
  border-top: 4px solid transparent;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

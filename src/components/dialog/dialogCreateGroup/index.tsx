import {
  Box,
  <PERSON>ton,
  Dialog, <PERSON>alogA<PERSON>,
  DialogContent, DialogTitle, InputAdornment, Stack, Typography,
} from "@mui/material";
import styles from "./styles.module.scss";
import FormProvider from "../../form/form-provider";
import Iconify from "../../iconify";
import {useForm} from "react-hook-form";
import InputSelectBase from "../../input/inputSelect";

const DialogCreateGroup = (props: any) => {
  const {
    openDialogCreateGroup,
    setOpenDialogCreateGroup,
    defaultOption,
    options,
  } = props;

  const defaultValues = {
    full_name: '',
    email: '',
  }

  const methods = useForm({
    defaultValues,
  });

  const { handleSubmit } = methods;


  const onSubmit = handleSubmit(async () => {
    console.log('submit');
  });

  const handleCloseModal = () => {
    setOpenDialogCreateGroup(false);
  };

  return (
    <Dialog open={openDialogCreateGroup} fullWidth maxWidth="sm">
      <FormProvider methods={methods} onSubmit={onSubmit}>
        <Box sx={{ py: 2, px: 3 }}>
          <DialogTitle sx={{ fontSize: "20px", fontWeight: "700", fontFamily: "Noto Sans JP", lineHeight: "30px" }}>新しいグループを作成します</DialogTitle>
          <DialogContent>
            <Box className={styles.nameBox}>
              <Box className={styles.titleName}>グループの名前</Box>
              <InputSelectBase
                name="group_id"
                keyword="group_id"
                type="text"
                // errorText={errors?.company_id}
                className={styles.inputCompany}
                // handleChange={handleChangeCompany}
                size="small"
                // value={selectedCompany ?? defaultOption?.value}
                placeholder="グループ #n"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start" className="inputAdornment" sx={{ padding: '8px', marginRight: 0 }}>
                      <Iconify icon="" width={20} />
                    </InputAdornment>
                  ),
                }}
                options={options}
                defaultOption={defaultOption}
              />
            </Box>

          </DialogContent>
          <DialogActions>
            <Stack direction="row" columnGap={2} sx={{ px: 2 }}>
              <Button sx={{ fontSize: "14px", fontWeight: "700", border: "1px solid #CDD7E1", color: "#32383E", fontFamily: "Noto Sans JP", px: 2 }} onClick={handleCloseModal}>
                キャンセル
              </Button>
              <Button type="submit" sx={{ fontSize: "14px", fontWeight: "700", backgroundColor: "#0B6BCB !important", color: "#FFFFFF", fontFamily: "Noto Sans JP", px: 2 }}>
                <Iconify icon="material-symbols:check" width={24} sx={{ mr: 1 }} />
                <Typography variant="subtitle1">追加</Typography>
              </Button>
            </Stack>
          </DialogActions>
        </Box>
      </FormProvider>
    </Dialog>
  )
};

export default DialogCreateGroup;

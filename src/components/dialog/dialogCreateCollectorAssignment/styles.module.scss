@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';

.formDialog {
  .titleCollectorAssignment {
    font-size: 14px !important;
    color: $sub-title-color;
    font-family: $primary-font;
  }

  .titleName {
    font-size: 14px;
    font-weight: 500;
  }

  .dialogCollectorAssignTitle {
    flex-direction: row;
    display: flex;
    justify-content: space-between;

    .dialogTitle {
      font-size: 20px;
      font-weight: 700;
      font-family: $primary-font;
      line-height: 30px;
    }
  }

  .btnCloseModal {
    font-size: 14px;
    font-weight: 700;
    border: 1px solid $border-input-color;
    color: $trashCanBlockLowColor;
    font-family: $primary-font;
  }

  .btnSave {
    font-size: 14px;
    background-color: $primary-button-color;
    color: $color-white;
    font-family: $primary-font;

    .textSave {
      font-family: $primary-font;
      font-size: 14px;
      font-weight: 700;
    }

    &:hover {
      background-color: $primary-button-color;
    }
  }

  .btnAddInputCollector {
    padding: 14px 15px 11px 10px !important;
    border-radius: 5px;
    border: 1px solid $primary-button-color !important;

    .iconAddCollector {
      color: $primary-button-color;
      margin-right: 5px;
    }

    .btnText {
      font-size: 14px;
      font-weight: 700;
      line-height: 16px;
      color: $primary-button-color;
      font-family: $primary-font;
    }
  }

  .iconDelete {
    margin-top: 30px !important;
    margin-left: 20px !important;
  }
}

.btnLoadingAssignCollector {
  padding: 0px 20px;
  border-radius: 6px;
  background-color: $primary-button-color;
}

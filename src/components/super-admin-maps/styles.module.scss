@import '../../assets/styles/style';
@import '../../assets/styles/variable';

.mapContainer {
  margin-top: 35px;
  height: 650px;
  width: 100%;

  .textTrashName {
    font-size: 16px;
    color: #0B0D0E;
    font-weight: 700;
    width: 100%;
    cursor: pointer;

    .textGroup {
      font-size: 12px;
      font-weight: 400;
    }
  }

  .btnNotification {
    background-color: #C41C1C;
    border-radius: 8px;
    margin-top: 12px;
    text-align: end;

    .textNotification {
      color: #FFFFFF;
    }

    &:hover {
      background-color: #C41C1C;
    }
  }

  .imageAdmin {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    margin-right: 6px;
  }
}

.customMarker {
  background-color: #a52a2a; /* <PERSON><PERSON>u nền tương tự như hình ảnh bạn cung cấp */
  color: white; /* <PERSON><PERSON>u chữ trắng */
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
  transform: translate(-50%, -100%);
}

.btnSave {
  border: 1px solid $paginate-border-color;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: $spa-primary-button-color !important;

  &:hover {
    background-color: $primary-button-color;
  }

  .saveText {
    font-size: 14px;
    font-weight: 700;
    color: white;
    font-family: "Noto Sans JP";
  }
}

.btnBack {
  cursor: pointer;
  border: 1px solid #CDD7E1 !important;
  background-color: white;
  padding: 6px 12px;

  .backText {
    font-size: 14px !important;
    font-weight: 700 !important;
    color: #32383E !important;
    font-family: "Noto Sans JP";
  }
}

.headerInfoWindow {
  gap: 10px;
}

.iconEdit {
  color: $sub-title-color;
}

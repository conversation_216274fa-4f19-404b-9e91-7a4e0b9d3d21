@import '../../assets/styles/style';
@import '../../assets/styles/variable';

.avatar {
  border-radius: 50%;
  width: 50px;
  height: 50px;

  &.active {
    padding: 3px;
    border: 2px solid $primary-button-color;
  }

  img {
    border-radius: 50%;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.sectionProfile {
  display: flex;
  flex-direction: column;

  .adminName {
    font-size: 1rem;
    font-weight: 600;
    line-height: 24.8px;
    color: $spa-primary-text-color;
  }

  .adminEmail {
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    line-height: 21px;
    color: $spa-deactive-bg-button-color;
  }
}

.iconContainer {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  width: 42px;
  height: 42px;

  &.active {
    background-color: $primary-bg-button-color;
  }
}

.badge {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: -4px;
  right: -6px;
  background-color: $primary-button-color;
  color: white;
  border-radius: 50%;
  font-size: 10px;
  font-weight: bold;
  line-height: 18px;
  width: 24px;
  height: 24px;
}

.textLink {
  text-decoration: none;
  height: 100%;
  align-items: center;
  display: flex;

  &:nth-child(1) {
    margin-left: 30px;
  }
}

.btnUser, .btnCompany, .btnTrash {
  display: flex;
  align-items: center;
  border-radius: 6px;
  white-space: pre-wrap !important;
  color: $spa-primary-text-color;
  cursor: pointer;
  transition: all 0.3s;
  background-color: transparent;
  padding: 8px 20px;

  &:hover {
    background-color: #F5F5F5;
  }

  .textMenu {
    margin-left: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
  }
}

.btnActive, .btnActiveSpa {
  padding: 8px 20px;
}

.btnActive {
  color: $primary-button-color;
  background-color: $primary-bg-button-color;
}

.btnActiveSpa {
  color: $spa-primary-button-color;
  background-color: $spa-primary-bg-button-color;
}

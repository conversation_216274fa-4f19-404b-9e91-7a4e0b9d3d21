@import '../../../../assets/styles/style';
@import '../../../../assets/styles/variable';

body {
  font-family: $primary-font;
}

.containerFluid {
  justify-content: center;
  align-items: center;
  margin-top: 15vh;

  .textCenter {
    text-align: center;
  }

  .subTitle {
    font-weight: 500;
    font-size: 16px;
    margin: 24px 0;
    line-height: 25px;
    font-family: $primary-font;
    color: $sub-title-color;
  }

  .subTitleEmail,
  .subTitlePassword {
    font-size: 14px;
    line-height: 21px;
    font-family: $primary-font;
    padding-bottom: 5px;
  }

  .btnSubmit {
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    padding: 12px 0;
    font-family: $primary-font;
  }
}

input[type="text"]::placeholder,
input[type="password"]::placeholder,
input[type="number"]::placeholder {
  color: $placeholder-input-color;
  font-size: 14px;
  font-weight: 400;
  line-height: 27.9px;
  font-family: $primary-font;
}

.successCoverBox {
  border: 1px solid #ebedf0;
  border-radius: 5px;
  padding: 50px;
}

.successMessage {
  text-align: center;
  margin-top: 20px;
}

.successTitle {
  font-size: 1.4rem;
  font-weight: bold;
  margin-bottom: 0.8rem;
}

.successSubtitle {
  font-size: 0.85rem;
}

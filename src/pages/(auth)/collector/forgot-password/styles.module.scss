@import '../../../../assets/styles/style';
@import '../../../../assets/styles/variable';

body {
  font-family: $primary-font;
}

.containerFluid {
  justify-content: center;
  align-items: center;
  margin-top: 15vh;

  .cardCoverBorderSolid {
    border: 1px solid $border-input-color;
    border-radius: 5px;
    padding: 50px;
    margin-top: -10vh;
  }

  .textCenter {
    text-align: center;
  }

  .subTitle {
    font-weight: 500;
    font-size: 16px;
    margin-top: 4px;
    margin-bottom: 10px;
    line-height: 25px;
    font-family: $primary-font;
    color: $sub-title-color;
  }

  .subTitleEmail {
    margin-bottom: 0;
    font-size: 14px;
    line-height: 21px;
    font-family: $primary-font;
    padding-bottom: 5px;
  }

  .inputEmail {
    padding: 12px 16px;
    border-radius: 8px;
  }

  .btnSubmit {
    width: 100%;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    padding: 12px 0;
    font-family: $primary-font;
  }
}

.successCoverBox {
  border: 1px solid #ebedf0;
  border-radius: 5px;
  padding: 50px;
}

.successMessage {
  text-align: center;
  margin-top: 20px;
}

.successTitle {
  font-size: 1.4rem;
  font-weight: bold;
  margin-bottom: 0.8rem;
}

.successSubtitle {
  font-size: 0.85rem;
}

.btnBackToLogin {
  width: 50%;
  margin-top: 0.8rem;
  font-family: $primary-font !important;
  font-weight: 600 !important;
}

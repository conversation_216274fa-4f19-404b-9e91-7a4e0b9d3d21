@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';

body {
  font-family: $primary-font;
}

.containerFluid {
  justify-content: center;
  margin-top: 15vh;

  .cardCoverBorderSolid {
    border: 1px solid $border-input-color;
    border-radius: 5px;
    padding: 50px;
    margin-top: -10vh;
  }

  .logo {
    text-align: center;
  }

  .subTitle {
    color: $sub-title-color;
    font-size: 16px;
    font-weight: 500;
    line-height: 24.8px;
    text-align: center;
    margin-top: 20px;
    font-family: $primary-font;
  }

  #labelEmail,
  #labelPassword {
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;
    color: $label-color;
    font-family: $primary-font;
    padding-bottom: 5px;
  }

  .inputEmail,
  .inputPassword {
    border-radius: 8px;
    border: 0;
    gap: 8px;
    height: 52px;

    .inputAdornmentPassword {
      padding-left: 4px;
    }
  }

  input[type="text"]::placeholder,
  input[type="password"]::placeholder {
    color: $placeholder-input-color;
    font-size: 14px;
    font-weight: 400;
    line-height: 27.9px;
    font-family: $primary-font;
  }

  .forgotPassword {
    display: flex;
    justify-content: flex-end;
    font-size: 15px;
    font-weight: 400;
    line-height: 24px;
    margin-top: 10px;
    text-decoration: underline;
    text-decoration-color: #0B6BCB;
  }

  .btnLogin {
    width: 100%;
    margin-top: 30px;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    padding: 12px 0;
    font-family: $primary-font;
  }

  .btnLoginLoading {
    width: 100%;
    margin-top: 30px;
    font-size: 16px;
    font-weight: 600;
    line-height: 16px;
    font-family: $primary-font;
    padding: 7px 0;
    border: 1px solid $paginate-border-color;
    border-radius: 6px;
    background-color: $primary-button-color;
  }
}

.btnResetPassLoading {
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  line-height: 16px;
  margin-top: 30px;
  font-family: $primary-font;
  padding: 7px 0;
  border: 1px solid $paginate-border-color;
  border-radius: 6px;
  background-color: $primary-button-color;
}

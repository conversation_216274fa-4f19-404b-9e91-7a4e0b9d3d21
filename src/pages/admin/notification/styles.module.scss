@import '../../../assets/styles/variable';

.cardContainer {
  border: none;
  box-shadow: none !important;
  margin: calc($H_DESKTOP + 30px) auto;

  .titlePage {
    font-family: $primary-font;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    color: $label-color;
  }

  .btnRead {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: $primary-button-color;

    .iconRead {
      margin-right: 15px;
      color: $primary-button-color;
    }

    .btnText {
      font-weight: 700;
      font-family: $primary-font;
    }
  }

  .sectionNotification {
    border-top: 1px solid var(--neutral-200, $border-notification-color);
  
    .aboutNotification, .aboutNotificationNotRead {
      padding: 18px;
      border-width: 0px 0px 1px 0px;
      border-style: solid;
      border-color: var(--neutral-200, $border-notification-color);

      .timeNotification {
        font-weight: 500;
        font-size: 14px;
        font-family: $primary-font;
        padding-top: 5px;
      }

      .overviewNotification {
        display: flex;
        align-items: center;

        .sectionBell {
          border-radius: 6px;
          margin-right: 10px;
        }

        .titleNotification {
          font-weight: 700;
          font-size: 16px;
          font-family: $primary-font;
          color: $label-color;
        }
      }

      .sectionTime {
        min-width: 100px;
        text-align: end;
      }
    }

    .aboutNotificationNotRead {
      cursor: pointer;
      background-color: $background-notification-color;

      &:hover {
        opacity: 0.7;
      }

      .timeNotification {
        color: $primary-button-color;
      }

      .sectionBell {
        background-color: $color-white;
      }
    }

    .aboutNotification {
      .timeNotification {
        color: $spa-deactive-bg-button-color;
      }

      .sectionBell {
        background-color: $primary-bg-button-color;
      }
    }

    .textNoNotification {
      font-size: 20px;
      font-family: $primary-font;
      font-weight: bold;
    }
  }

  .sectionLoadMore {
    display: flex;
    justify-content: center;

    .overviewLoadMore {
      cursor: pointer;
      display: flex;
      align-items: center;

      .textLoadMore {
        margin-right: 20px;
        font-family: $primary-font;
        font-weight: 700;
        font-size: 14px;
        color: $primary-button-color;
      }

      .iconDots {
        color: $primary-button-color;
      }
    }
  }
}

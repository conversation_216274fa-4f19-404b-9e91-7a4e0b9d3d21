@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';

.cardContainer {
  border: none;
  box-shadow: none !important;
  margin-top: calc($H_DESKTOP + 30px);

  .sectionAction {
    margin-left: 123px;
    padding: 3px 0;
    display: flex;
    justify-content: flex-end;
    align-items: baseline;

    .boxIconMap {
      cursor: pointer;
      padding-right: 30px;

      .iconMap {
        color: $sub-title-color;
      }
    }

    .boxIconEdit {
      cursor: pointer;
      padding-right: 30px;
    }
  }

  .sectionActionNotNoti {
    margin-right: 112px;
    padding: 3px 0;
    display: flex;
    justify-content: flex-end;
    align-items: baseline;

    .boxIconMap {
      cursor: pointer;

      .iconMap {
        color: $sub-title-color;
      }
    }

    .boxIconEdit {
      cursor: pointer;
      padding-right: 30px;
    }
  }

  .postcalBox,
  .prefectureBox,
  .cityBox {
    width: 32%;
  }

  .imageAvatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    cursor: pointer;
  }

  .bigAvatar {
    width: 84px;
    height: 84px;
    border-radius: 50%;
  }

  .titlePage {
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    color: $title-page-color;
  }

  .companyName {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: $spa-primary-text-color;
    margin-top: 5px;
  }

  .searchContainer {
    .inputSearch {
      &::placeholder {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }

      input {
        height: 43px !important;
      }
    }
  }
}

.titleAdminDetail {
  font-family: "Noto Sans JP";
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  margin-bottom: 3px;
}

a {
  text-decoration: none !important;
}

.linkDiv {
  display: flex;
  align-items: center;
}

.optionBoxAdminList {
  display: block;
  position: absolute;
  background-color: $input-bg-color;
  color: $spa-primary-text-color;
  border: 1px solid $border-input-color;
  border-radius: 8px;
  font-family: $primary-font;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  width: 180px;
  right: -80px;
  z-index: 1;
}

.optionBoxAssignCollector {
  display: block;
  position: absolute;
  background-color: $input-bg-color;
  color: $spa-primary-text-color;
  border: 1px solid $border-input-color;
  border-radius: 8px;
  font-family: $primary-font;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  width: 120px;
  right: -80px;
  z-index: 1;
}

.optionText {
  color: #32383E;
  margin-left: 10px;
}

.borderTableHeader {
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-bottom: 2px solid $border-input-color;
}

.btnActive {
  background-color: $primary-button-color;
  color: $color-white;
  font-size: 14px;
  font-weight: bold;
  padding: 3px 6px;
  border-radius: 14px;
}

.btnInActive {
  background-color: $paginate-bg-color;
  color: $spa-deactive-bg-button-color;
  font-size: 14px;
  font-weight: bold;
  padding: 3px 10px;
  border-radius: 14px;
}

.textTitle {
  font-size: 20px;
  font-weight: bold;
}

.boxContainer {
  width: 100%;
  margin-left: 32px;
}

.textSubtitle {
  color: $sub-title-color;
  font-size: 16px;
}

.iconWithSubColor {
  color: $sub-title-color;
}

.infoUser {
  display: flex;
  align-items: center;
  margin-top: 10px;

  > div {
    margin-left: 10px;
  }
}

.tableContainer {
  .actions {
    .iconChecked, .iconNotChecked {
      cursor: pointer;
    }

    .iconNotChecked {
      color: $sub-title-color;
    }

    .iconChecked {
      color: $primary-button-color;
    }
  }
}

.txtCollectorName {
  font-weight: 700;
}

.firstInfoCollector {
  display: flex;
  align-items: center;
  font-weight: 700;
  font-size: 20px;
}

.btnSubmitDialog {
  font-size: 14px !important;
  font-weight: 700 !important;
  border: 1px solid $border-input-color;
  color: #FFFFFF !important;
  background-color: $btn-red-bgcolor !important;
  cursor: pointer;
}

@media (max-width: 1440px)  {
  .cardContainer {
    .sectionAction {
      margin-left: 103px;
    }
    .sectionActionNotNoti {
      margin-left: 103px;

    }
  }
}

@media (max-width: 1350px)  {
  .cardContainer {
    .sectionAction {
      margin-left: 53px;
    }
    .sectionActionNotNoti {
      margin-left: 53px;

    }
  }
}

.popover {
  height: 400px;
}

.popUpCollector {
  width: 200px;
}

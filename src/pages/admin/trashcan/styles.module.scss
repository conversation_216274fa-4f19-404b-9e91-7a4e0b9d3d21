@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';

.cardContainer {
  border: none;
  box-shadow: none !important;
  margin-top: calc($H_DESKTOP + 30px);

  .descriptionTable {
    font-family: $primary-font;
    font-size: 14px;
    color: $label-color;
  }

  .sectionNotification {
    display: flex;
    align-items: baseline;
    padding: 5px;
    border: 1px solid $deactivate-action-bg-color;
    border-radius: 8px;

    .numberTrashFull {
      font-family: $primary-font;
      padding-right: 60px;
      color: $btn-red-bgcolor;
      font-size: 15px;
    }
  }

  .titlePage {
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    color: $label-color;
    font-family: $primary-font;
  }

  .btnTabs1,
  .btnTabs2 {
    padding: 11px 25px;
    border: 1px solid $border-input-color;
    border-radius: 5px;
    border-radius: inherit;
    color: $label-color;

    .iconChild {
      color: $label-color;
      margin-right: 5px;
    }

    .btnText {
      font-size: 1rem;
      font-weight: 700;
      line-height: 16px;
      color: $placeholder-input-color;
      font-family: $primary-font;
    }

    &.btnSelected {
      background-color: $border-input-color;
    }
  }

  .btnTabs1 {
    border-radius: 5px 0 0 5px;
  }

  .btnTabs2 {
    border-radius: 0 5px 5px 0;
  }

  .btnAddGroup {
    padding: 14px 15px 11px 10px;
    border-radius: 5px;
    margin-left: 10px;
    border: 1px solid $primary-button-color;

    .iconAddGroup {
      color: $primary-button-color;
      margin-right: 5px;
    }

    .btnText {
      font-size: 1rem;
      font-weight: 700;
      line-height: 16px;
      color: $primary-button-color;
      font-family: $primary-font;
    }
  }

  .sectionAction {
    display: flex;
    justify-content: flex-end;
    align-items: baseline;

    .boxIconMap {
      cursor: pointer;
      margin-right: 30px;

      .iconMap {
        color: $sub-title-color;
      }
    }

    .boxIconEdit {
      cursor: pointer;
      margin-right: 30px;
    }
  }

  .sectionActionNotNoti {
    display: flex;
    justify-content: flex-end;
    align-items: baseline;
    margin-right: 112px;

    .boxIconMap {
      cursor: pointer;
      margin-right: 30px;

      .iconMap {
        color: $sub-title-color;
      }
    }

    .boxIconEdit {
      cursor: pointer;
    }
  }

  .searchContainer {
    padding-top: 42px;

    .labelInput {
      font-family: $primary-font;
      font-size: 14px;
      font-weight: 500;
      color: $label-color;
      margin-bottom: 6px;
    }

    .select2Style {
      width: 100%;
      border-radius: 8px;
    }
  }
}

.borderTableHeader {
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-bottom: 2px solid $paginate-border-color;
}

.css-19xm0h7-MuiButtonBase-root-MuiPaginationItem-root.Mui-selected {
  background-color: $primary-button-color;
}

.titleInput {
  margin-bottom: 5px;
  font-family: $primary-font;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: $label-color;
}

.popover {
  max-height: 400px;
}

.managerAvatarPopup {
  display: inline-table;
}

.css-1e6y48t-MuiButtonBase-root-MuiButton-root:hover {
  background-color: $spa-primary-button-color;
}

.css-lr07df-MuiButtonBase-root-MuiButton-root-MuiLoadingButton-root {
  padding: 0 !important;
}

.css-lr07df-MuiButtonBase-root-MuiButton-root-MuiLoadingButton-root:hover {
  background-color: red;
}

.imageAvatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 2px;
  object-fit: cover;
}

.listCollectorAssign {
  font-weight: 700;
  border-bottom: 1px solid $trashCanBlockEmptyColor;
  padding-bottom: 10px;
}

.popUpMoreCollector {
  width: 200px;
}

@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';

.cardContainer {
  border: none;
  box-shadow: none !important;
  margin-top: calc($H_DESKTOP + 30px);

  .selectedItem {
    background-color: inherit !important;
    color: #0B6BCB;
    font-size: 14px;
    font-family: $primary-font;
  }

  .iconArrow {
    position: absolute;
    right: 0;
    top: 15px;
    font-size: 15px;
  }

  .selectedIcon {
    color: #0B6BCB !important;
  }

  .titlePage {
    font-family: $primary-font;
    font-size: 24px;
    font-weight: 700;
    line-height: 36px;
    color: $label-color;
  }

  .sectionMenu {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .textInfoAdmin {
      font-family: $primary-font;
    }

    .sectionName {
      display: flex;
      padding-bottom: 5px;

      .fullName {
        font-weight: 700;
        font-size: 20px;
        margin-right: 15px;
        font-family: $primary-font;
      }

      .status {
        font-family: $primary-font;
      }
    }

    .imageAvatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      margin-right: 25px;
      object-fit: cover;
    }

    .btnActive {
      background-color: $primary-button-color;
      color: $color-white;
      font-size: 14px;
      font-weight: bold;
      padding: 6px;
      border-radius: 14px;
      margin-left: 0;
    }

    .btnInActive {
      background-color: $paginate-bg-color;
      color: $spa-deactive-bg-button-color;
      font-size: 14px;
      font-weight: bold;
      padding: 3px 10px;
      border-radius: 14px;
      margin-left: 0;
    }
  }

  .btnUploadImage {
    border: 1px solid $primary-bg-color;
    font-size: 14px;
    font-weight: 700;
    font-family: $primary-font;
    padding: 6px 12px;
    border-radius: 6px;
    color: $primary-button-color;
    cursor: pointer;
  }
}

@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';


.cardContainer {
  border: none;
  box-shadow: none !important;
  margin-top: calc($H_DESKTOP + 30px);

  .titlePage {
    font-size: 22px;
    font-weight: 700;
    line-height: 36px;
    color: $spa-title-page-color;
    font-family: $primary-font;
  }

  .btnTabs1, .btnTabs2 {
    height: 42px;
    padding: 0 25px;
    border: 1px solid $border-input-color;
    border-radius: 5px;
    border-radius: inherit;
    color: $label-color;

    .iconChild {
      color: $label-color;
      margin-right: 5px;
    }

    .btnText {
      font-size: 16px;
      font-weight: 700;
      line-height: 16px;
      color: $placeholder-input-color;
      font-family: $primary-font;
    }

    &.btnSelected {
      background-color: $border-input-color;
    }
  }

  .btnTabs1 {
    border-radius: 5px 0 0 5px;
  }

  .btnTabs2 {
    border-radius: 0 5px 5px 0;
  }

  .iconMap {
    color: $sub-title-color;
  }

  .searchContainer {
    padding-top: 16px;
  }
}

.borderTableHeader {
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-bottom: 2px solid $paginate-border-color;
}

.css-19xm0h7-MuiButtonBase-root-MuiPaginationItem-root.Mui-selected {
  background-color: $primary-button-color;
}

.titleInput {
  margin-bottom: 5px;
  font-family: $primary-font;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: $label-color;
}

.btnCreate {
  font-size: 14px;
  font-weight: 700;
  border: 1px solid $paginate-border-color;
  color: white;
  font-family: $primary-font;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: $spa-primary-button-color;
}

.css-1e6y48t-MuiButtonBase-root-MuiButton-root:hover {
  background-color: $spa-primary-button-color;
}

.labelSearch {
  font-size: 14px;
  color: $label-color;
}

.titleStatus {
  color: $spa-primary-text-color;
}

.btnSendNotification {
  font-size: 14px;
  font-weight: 700;
  border: 1px solid $paginate-border-color;
  background-color: $btn-red-bgcolor;
  color: $trashCanBlockLowColor;
  font-family: $primary-font;
  padding: 6px 12px;
  border-radius: 6px;
  height: 40px;
  margin-top: 5px;

  .textNotification {
    color: #FFFFFF;
  }

  &:hover {
    background-color: $btn-red-bgcolor;
  }
}

.btnDeleteTrashcan {
  padding: 14px 15px 11px 10px !important;
  border-radius: 5px;
  border: 1px solid $trashCanBlockHighColor !important;

  .iconAddCollector {
    color: $trashCanBlockHighColor;
    margin-right: 5px;
  }

  .btnText {
    font-size: 16px;
    font-weight: 700;
    line-height: 16px;
    color: $trashCanBlockHighColor !important;
    font-family: $primary-font;
  }
}

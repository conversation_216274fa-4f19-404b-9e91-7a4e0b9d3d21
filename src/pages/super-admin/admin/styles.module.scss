@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';

body {
  font-family: $primary-font;
}

.cardContainer {
  border: none;
  box-shadow: none !important;
  margin-top: calc($H_DESKTOP + 30px);

  .imageAvatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
  }

  .bigAvatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
  }

  .titlePage {
    font-size: 22px;
    font-weight: 700;
    line-height: 36px;
    color: $label-color;
  }

  .companyName {
    font-size: 14px;
    font-weight: 400;
    line-height: 24px;
    color: $spa-primary-text-color;
    margin-top: 5px;
  }

  .btnEdit {
    border: 1px solid $trashCanBlockMediumLowColor;
    color: $spa-primary-button-color;
    background-color: $color-white;
    margin-left: 16px;
    padding: 8px 5px;
    font-family: $primary-font;
    font-size: 16px;
    font-weight: 700;
    width: 90px;

    .iconEdit {
      margin-right: 10px;
    }
  }

  .searchContainer {
    padding-top: 22px;

    .inputSearch {
      background-color: $input-bg-color;

      &::placeholder {
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
      }

      input {
        height: 43px !important;
      }
    }
  }

  .btnDeactiveAction {
    font-size: 14px;
    font-weight: 700;
    border: 1px solid $deactivate-action-bg-color;
    color: $btn-red-bgcolor;
    background-color: white;
    font-family: $primary-font;
    padding: 8px 24px;
  }

  .btnActiveAction {
    font-size: 14px;
    font-weight: 700;
    border: 1px solid $spa-primary-border-btn-color;
    color: $spa-primary-button-color;
    background-color: white;
    font-family: $primary-font;
    padding: 8px 24px;
  }

  .btnActive {
    background-color: $spa-primary-button-color;
    color: #FFFFFF;
    font-size: 14px;
    font-weight: bold;
    padding: 3px 6px;
    border-radius: 14px;
  }

  .btnInActive {
    background-color: $paginate-bg-color;
    color: $spa-deactive-bg-button-color;
    font-size: 14px;
    font-weight: bold;
    padding: 3px 10px;
    border-radius: 14px;
  }

  .btnSubmit {
    font-size: 1rem;
    font-weight: 700;
    color: white !important;
    background-color: $spa-primary-button-color;
    font-family: $primary-font;
    padding: 8px 24px;

    &:hover {
      background-color: $spa-primary-button-color;
    }
  }

  .iconSubmit {
    margin-right: 6px;
    font-size: 1.1rem;
  }

  .postcalBox,
  .prefectureBox,
  .cityBox {
    width: 32%;
  }
}

.titleName,
.titleCompany,
.titleEmail {
  font-family: $primary-font;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  text-align: left;
}

.inputName,
.inputCompany,
.inputEmail {
  margin-top: 5px;
}

input[type="text"]::placeholder {
  color: $placeholder-input-color;
  font-size: 14px;
  font-weight: 400;
  line-height: 27.9px;
  font-family: $primary-font;
}

.optionBoxAdminList {
  position: absolute;
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-radius: 8px;
  font-family: $primary-font;
  font-size: 16px;
  line-height: 24px;
  z-index: 1;
  min-width: 180px;
  transform: translateX(-40px);
}

.optionText {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: $spa-primary-text-color;
  margin-left: 10px;
}

.inputNameAdmin {
  width: 100%;
}

.titleAdminDetail {
  font-family: $primary-font;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  margin-bottom: 3px;
}

a {
  text-decoration: none !important;
}

.linkDiv {
  display: flex;
  align-items: center;
}

.adminName {
  font-family: $primary-font;
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
}

.borderTableHeader {
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-bottom: 2px solid $border-input-color;
}

.titleStatus {
  font-family: $primary-font;
  font-size: 14px;
  font-weight: 700;
  line-height: 21px;
}

.btnSwitchToEditCompany {
  color: white !important;
  background-color: $spa-primary-button-color !important;
}

.textButton {
  font-family: "Noto Sans JP" !important;
  font-size: 16px !important;
  font-weight: 700 !important;
}

.infoUser {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  color: $sub-title-color;
  font-family: $primary-font;
  font-size: 16px;
  font-weight: 500;
  line-height: 24.8px;
}

.fullNameAdmin {
  font-family: $primary-font;
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
}

@import '../../../assets/styles/style';
@import '../../../assets/styles/variable';

.cardContainer {
  border: none;
  box-shadow: none !important;
  margin-top: calc($H_DESKTOP + 30px);

  .iconCompany {
    width: 50px;
    height: 50px;
    color: $sub-title-color;
  }

  .textCommon {
    font-family: $primary-font !important;
  }

  .borderTableHeader {
    background-color: $input-bg-color;
    border: 1px solid $border-input-color;
    border-bottom: 2px solid $border-input-color;
  }

  .boxContainerCompany {
    width: 100%;
    margin-left: 32px;

    .textTitle {
      font-weight: 700;
      font-family: $primary-font;
      font-size: 20px;
    }
  }

  .btnEdit {
    border: 1px solid $trashCanBlockMediumLowColor;
    color: $spa-primary-button-color;
    background-color: $color-white;
    margin-left: 16px;
    padding: 8px 5px;
    font-family: $primary-font;
    font-size: 16px;
    font-weight: 700;
    width: 90px;

    .iconEdit {
      margin-right: 10px;
    }
  }

  .linkDiv {
    display: flex;
    align-items: center;
  }

  .infoUser {
    display: flex;
    gap: 10px;
    padding: 5px 0;
    color: $sub-title-color;
    font-family: $primary-font;
    font-size: 16px;
    font-weight: 500;
    line-height: 24.8px;
  }

  .titlePage {
    font-size: 22px;
    font-weight: 700;
    line-height: 36px;
    color: $title-page-color;
  }

  .companyName {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: $spa-primary-text-color;
    margin-top: 5px;
  }

  .searchContainer {
    .inputSearch {
      &::placeholder {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }

      input {
        height: 43px !important;
      }
    }
  }
}

.btnActive {
  width: 100%;
  height: 100%;
  background-color: #0B6BCB;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  padding: 8px;
  border-radius: 16px;
}

.btnInActive {
  width: 100%;
  height: 100%;
  background-color: #F0F4F8;
  color: #9FA6AD;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  padding: 8px;
  border-radius: 16px;
}

.titleInputAddCompany,
.titleName {
  font-family: $primary-font;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  color: #171A1C;
}

.optionCompanyBox {
  position: absolute;
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-radius: 8px;
  font-family: $primary-font;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  z-index: 10;
  width: 100px;
  right: -80px;
}

.optionText {
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  color: $trashCanBlockLowColor;
  margin-left: 10px;
}

.borderTableHeader {
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-bottom: 2px solid $paginate-border-color;
}

.btnCreate {
  font-size: 14px;
  font-weight: 700;
  border: 1px solid $paginate-border-color;
  color: white;
  font-family: $primary-font;
  padding: 6px 12px;
  border-radius: 6px;
  background-color: $spa-primary-button-color;
}

.btnSwitchToEditCompany {
  color: white !important;
  background-color: $spa-primary-button-color !important;

  .iconSubmit {
    margin-right: 6px;
    font-size: 1.1rem;
  }
}

.textButton {
  font-family: $primary-font !important;
  font-size: 16px !important;
  font-weight: 700 !important;
}

.statusActiveAdmin {
  background-color: $spa-primary-button-color;
  color: #FFFFFF;
  font-size: 14px;
  font-weight: bold;
  padding: 3px 6px;
  border-radius: 14px;
}

.statusInActiveAdmin {
  background-color: $paginate-bg-color;
  color: $spa-deactive-bg-button-color;
  font-size: 14px;
  font-weight: bold;
  padding: 3px 10px;
  border-radius: 14px;
}

.btnBackAdminInfo {
  font-size: 14px !important;
  font-weight: 700 !important;
  border: 1px solid #CDD7E1 !important;
  color: #32383E !important;
  cursor: pointer;
  padding: 10px !important;
}

@import 'variable';

.textCommon {
  font-family: $primary-font !important;
}

.borderTable {
  border: 1px solid $border-input-color,
}

.colorTable {
  color: $trashCanBlockLowColor;
}

.textTable {
  font-size: 16px;
  font-weight: 400;
}

.boxContainer {
  width: 100%;
  margin-left: 32px;
}

.errorText {
  color: $error-text-color;
  font-family: $primary-font;
  font-size: 12px;
}

.centerFlex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.optionBox {
  position: absolute;
  background-color: $input-bg-color;
  border: 1px solid $border-input-color;
  border-radius: 8px;
  font-family: $primary-font;
  font-size: 16px;
  line-height: 24px;
  z-index: 1;
}

.btnAdd, .btnEdit, .btnBack, .btnAddSpa, .btnNotificationAll, .btnEditSpa, .btnSubmit, .btnSwitchToEditCompany {
  padding: 12px !important;
  height: 42px;
  border-radius: 5px !important;
  transition: all .3s !important;
  font-family: $primary-font !important;

  .iconAdd {
    color: $color-white;
    margin-right: 5px;
  }

  .btnText {
    font-size: 14px !important;
    line-height: 16px;
    color: $color-white;
    font-family: $primary-font !important;
    font-weight: 700;
  }

  &:disabled {
    opacity: 0.7;
  }
}

.btnAdd {
  background-color: $primary-button-color !important;
  color: $color-white !important;

  &:hover {
    background-color: $primary-button-color;
  }
}

.btnEdit {
  border: 1px solid $border-button-color !important;

  .btnText {
    color: $primary-button-color !important;
  }

  .iconEdit {
    margin-right: 5px;
  }
}

.btnEditSpa {
  border: 1px solid $spa-primary-button-color !important;

  .btnText {
    color: $spa-primary-button-color !important;
  }

  .iconEdit {
    margin-right: 5px;
    color: $spa-primary-button-color !important;
  }
}

.btnAddSpa {
  background-color: $spa-primary-button-color !important;
  color: $color-white !important;

  &:hover {
    background-color: $spa-primary-button-color;
  }
}

.btnNotificationAll {
  background-color: $btn-red-bgcolor !important;

  &:hover {
    background-color: $btn-red-bgcolor;
  }
}

.btnBack {
  border: 1px solid $border-input-color !important;
  color: $spa-primary-text-color !important;
  font-weight: 700 !important;

  .btnText {
    color: $spa-primary-text-color !important;
  }

  &:hover {
    background-color: transparent !important;
  }
}

.btnNotification {
  background-color: $btn-red-bgcolor !important;
  border-radius: 8px !important;
  width: 82px;

  &:hover {
    background-color: $btn-red-bgcolor !important;
  }

  .textNotification {
    color: $color-white !important;
    font-weight: bold !important;
    font-family: $primary-font !important;
  }
}

.searchContainer {
  .btnSearch, .btnCancel, .btnSearchSpa {
    width: 120px;
    height: 43px;
    border-radius: 6px;
    font-family: $primary-font !important;

    .searchText {
      font-family: $primary-font;
      font-size: 14px;
      font-weight: bold;
    }
  }

  .btnSearch {
    border: 1px solid $border-button-color;

    .searchText {
      color: $primary-button-color;
    }
  }

  .btnCancel {
    border: 1px solid $paginate-border-color;

    .searchText {
      color: $trashCanBlockLowColor;
    }
  }

  .btnSearchSpa {
    border: 1px solid $spa-primary-button-color;

    .searchText {
      color: $spa-primary-button-color;
    }
  }
}

.tableContainer {
  position: relative;
  overflow: unset;
  margin-top: 24px;
  overflow-x: auto;

  tr:not(:last-child) td {
    padding: 0 16px !important;
    height: 40px;
  }
}

.iconRequired {
  font-size: 14px;
  margin-left: 3px;
  color: $required-icon-color;
}

.textPlaceholder {
  font-family: $primary-font;
  font-size: 14x;
  font-weight: 400;
  line-height: 27.9px;
  color: $placeholder-input-color;
}

input[type="text"]::placeholder,
input[type="password"]::placeholder {
  color: $placeholder-input-color;
  font-size: 14px;
  font-weight: 400;
  line-height: 27.9px;
  font-family: $primary-font !important;
}

.primaryFontSize {
  font-size: 14px;
}

.btnLoading {
  border: 1px solid $paginate-border-color;
  padding: 6px 24px;
  border-radius: 6px;
  background-color: $primary-button-color;
}

.btnLoadingDelete {
  border: 1px solid $btn-red-bgcolor;
  padding: 6px 24px;
  border-radius: 6px;
  background-color: $btn-red-bgcolor;
  width: 100px;
}

.longText2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.btnLoadingSpa {
  border: 1px solid $paginate-border-color;
  padding: 6px 24px;
  border-radius: 6px;
  background-color: $spa-primary-button-color;
}

.spaPrimaryTextColor {
  color: $spa-primary-text-color;
}

.titleDialog {
  font-size: 20px;
  font-weight: 700;
  line-height: 30px;
}

.contentDialog {
  font-size: 16px;
  white-space: pre-line;
}

.textBold {
  font-weight: bold !important;
}

.pointer {
  cursor: pointer;
}

.truncateText {
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
  display: block;
  white-space: nowrap;
}

.itemPopper {
  padding: 8px;
  cursor: pointer;
  flex-direction: row !important;
  align-items: center;

  .iconPopper {
    width: 20px;
    color: $sub-title-color;
  }
}

.inputSkeleton {
  height: 3rem !important;
  border-radius: 6px;
  margin-top: 20px;
}

.labelSkeleton {
  height: 1.5rem !important;
  border-radius: 6px;
  margin-top: 5px;
  width: 10%;
}

.imageAssignAvatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}
